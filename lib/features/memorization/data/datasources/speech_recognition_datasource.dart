import 'dart:async';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/errors/exceptions.dart' as app_exceptions;

abstract class SpeechRecognitionDatasource {
  Future<void> initialize();
  Future<bool> startListening();
  Future<void> stopListening();
  Stream<String> get speechStream;
  Future<bool> get isAvailable;
  Future<bool> get hasPermission;
  Future<void> dispose();
}

class SpeechRecognitionDatasourceImpl implements SpeechRecognitionDatasource {
  final SpeechToText _speechToText = SpeechToText();
  final StreamController<String> _speechController =
      StreamController<String>.broadcast();
  bool _isInitialized = false;
  bool _isListening = false;

  @override
  Future<void> initialize() async {
    try {
      if (_isInitialized) return;

      final available = await _speechToText.initialize(
        onError: (error) {
          print('Speech recognition error: ${error.errorMsg}');
          // Only throw exceptions for critical errors, not for timeout or no match
          if (_isCriticalError(error.errorMsg)) {
            _speechController.addError(
              app_exceptions.SpeechRecognitionException(
                'خطأ في التعرف على الصوت: ${error.errorMsg}',
                code: error.toString(),
              ),
            );
          }
        },
        onStatus: (status) {
          print('Speech recognition status: $status');
          if (status == 'done' || status == 'notListening') {
            _isListening = false;
          }
        },
      );

      if (!available) {
        throw app_exceptions.SpeechRecognitionException(
          'التعرف على الصوت غير متاح على هذا الجهاز. يرجى التأكد من أن الجهاز يدعم التعرف على الصوت العربي.',
        );
      }

      _isInitialized = true;
      print('Speech recognition initialized successfully');
    } catch (e) {
      print('Failed to initialize speech recognition: $e');
      if (e is app_exceptions.AppException) {
        rethrow;
      }
      throw app_exceptions.SpeechRecognitionException(
        'فشل في تهيئة التعرف على الصوت: $e',
      );
    }
  }

  @override
  Future<bool> startListening() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      if (_isListening) {
        await stopListening();
      }

      final hasPermission = await this.hasPermission;
      if (!hasPermission) {
        throw app_exceptions.PermissionException(
          AppConstants.microphonePermissionError,
        );
      }

      await _speechToText.listen(
        onResult: (result) {
          if (result.recognizedWords.isNotEmpty) {
            _speechController.add(result.recognizedWords);
          }
        },
        onSoundLevelChange: (level) {
          // Optional: Handle sound level changes for UI feedback
        },
        listenFor: AppConstants.speechTimeout,
        pauseFor: AppConstants.pauseTimeout,
        localeId: AppConstants.speechLocale,
        listenOptions: SpeechListenOptions(
          partialResults: true,
          cancelOnError: false,
          listenMode: ListenMode.confirmation,
        ),
      );

      _isListening = true;
      return true;
    } catch (e) {
      _isListening = false;
      if (e is app_exceptions.AppException) {
        rethrow;
      }
      throw app_exceptions.SpeechRecognitionException(
        'Failed to start listening: $e',
      );
    }
  }

  @override
  Future<void> stopListening() async {
    try {
      if (_isListening) {
        await _speechToText.stop();
        _isListening = false;
      }
    } catch (e) {
      throw app_exceptions.SpeechRecognitionException(
        'Failed to stop listening: $e',
      );
    }
  }

  @override
  Stream<String> get speechStream => _speechController.stream;

  @override
  Future<bool> get isAvailable async {
    try {
      if (!_isInitialized) {
        await initialize();
      }
      final available = _speechToText.isAvailable;
      print('Speech recognition availability: $available');
      return available;
    } catch (e) {
      print('Error checking speech recognition availability: $e');
      return false;
    }
  }

  @override
  Future<bool> get hasPermission async {
    try {
      final status = await Permission.microphone.status;
      if (status.isGranted) {
        return true;
      }

      if (status.isDenied) {
        final result = await Permission.microphone.request();
        return result.isGranted;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> dispose() async {
    try {
      await stopListening();
      await _speechController.close();
    } catch (e) {
      // Ignore disposal errors
    }
  }

  /// Determines if a speech recognition error is critical and should throw an exception
  bool _isCriticalError(String errorMsg) {
    // These are normal speech recognition events that shouldn't throw exceptions
    const nonCriticalErrors = [
      'error_speech_timeout',
      'error_no_match',
      'error_busy',
      'error_audio',
    ];

    return !nonCriticalErrors.contains(errorMsg);
  }
}
