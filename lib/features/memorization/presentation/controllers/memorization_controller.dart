import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../../core/services/dependency_injection.dart';
import '../../../../core/errors/failures.dart';
import '../../../../core/utils/audio_utils.dart';
import '../../../text_management/domain/entities/saved_text.dart';
import '../../domain/entities/memorization_session.dart';
import '../../domain/entities/word_match_result.dart';
import '../../domain/usecases/start_session.dart';
import '../../domain/usecases/process_speech.dart';
import '../../domain/usecases/validate_word.dart';
import '../../data/datasources/speech_recognition_datasource.dart';

part 'memorization_controller.freezed.dart';

@freezed
class MemorizationState with _$MemorizationState {
  const factory MemorizationState({
    MemorizationSession? session,
    @Default([]) List<String> revealedWords,
    @Default(0) int currentWordIndex,
    @Default(false) bool isListening,
    @Default(false) bool isInitializing,
    @Default(false) bool isCompleted,
    @Default(false) bool hasError,
    String? errorMessage,
    String? lastSpokenWord,
    @Default('') String currentTranscription,
    WordMatchResult? lastMatchResult,
  }) = _MemorizationState;
}

class MemorizationController extends StateNotifier<MemorizationState> {
  final StartSession _startSession;
  final ProcessSpeech _processSpeech;
  final ValidateWord _validateWord;
  final SpeechRecognitionDatasource _speechDatasource;

  StreamSubscription<String>? _speechSubscription;
  StreamSubscription<String>? _partialSpeechSubscription;

  MemorizationController({
    required StartSession startSession,
    required ProcessSpeech processSpeech,
    required ValidateWord validateWord,
    required SpeechRecognitionDatasource speechDatasource,
  }) : _startSession = startSession,
       _processSpeech = processSpeech,
       _validateWord = validateWord,
       _speechDatasource = speechDatasource,
       super(const MemorizationState());

  /// Starts a new memorization session
  Future<void> startMemorizationSession(SavedText savedText) async {
    state = state.copyWith(
      isInitializing: true,
      hasError: false,
      errorMessage: null,
    );

    try {
      // Validate the saved text before starting session
      if (!savedText.isValid) {
        state = state.copyWith(
          isInitializing: false,
          hasError: true,
          errorMessage: 'النص غير صالح أو لا يحتوي على أحرف عربية',
        );
        return;
      }

      final words = savedText.words;

      // Check if words list is empty
      if (words.isEmpty) {
        state = state.copyWith(
          isInitializing: false,
          hasError: true,
          errorMessage: 'النص لا يحتوي على كلمات صالحة',
        );
        return;
      }

      final result = await _startSession(savedText.id, words);

      result.fold(
        (failure) {
          state = state.copyWith(
            isInitializing: false,
            hasError: true,
            errorMessage: failure.userMessage,
          );
        },
        (session) {
          state = state.copyWith(
            isInitializing: false,
            session: session,
            revealedWords: List.filled(words.length, ''),
            currentWordIndex: 0,
            isCompleted: false,
            hasError: false,
            errorMessage: null,
          );

          // Start listening for speech
          _startListening();
        },
      );
    } catch (e) {
      state = state.copyWith(
        isInitializing: false,
        hasError: true,
        errorMessage: 'فشل في بدء الجلسة: $e',
      );
    }
  }

  /// Starts listening for speech input
  Future<void> _startListening() async {
    try {
      final started = await _speechDatasource.startListening();
      if (started) {
        state = state.copyWith(isListening: true);

        // Listen to speech stream for final results
        _speechSubscription?.cancel();
        _speechSubscription = _speechDatasource.speechStream.listen(
          _onSpeechResult,
          onError: _onSpeechError,
        );

        // Listen to partial speech stream for real-time transcription
        _partialSpeechSubscription?.cancel();
        _partialSpeechSubscription = _speechDatasource.partialSpeechStream.listen(
          _onPartialSpeechResult,
          onError: (error) {
            // Handle partial speech errors silently to avoid disrupting the UI
          },
        );
      }
    } catch (e) {
      state = state.copyWith(
        hasError: true,
        errorMessage: 'Failed to start listening: $e',
      );
    }
  }

  /// Handles partial speech recognition results for real-time transcription
  void _onPartialSpeechResult(String partialText) {
    if (state.session == null || state.isCompleted) return;

    // Update the current transcription with partial results
    state = state.copyWith(currentTranscription: partialText);
  }

  /// Handles speech recognition results
  void _onSpeechResult(String spokenText) async {
    if (state.session == null || state.isCompleted) return;

    final currentWord = state.session!.words[state.currentWordIndex];
    state = state.copyWith(
      lastSpokenWord: spokenText,
      currentTranscription:
          '', // Clear transcription when final result is received
    );

    // Process the spoken word
    final result = await _processSpeech(spokenText, currentWord);

    result.fold(
      (failure) {
        state = state.copyWith(
          hasError: true,
          errorMessage: failure.userMessage,
        );
      },
      (matchResult) {
        state = state.copyWith(lastMatchResult: matchResult);

        if (matchResult.isMatch) {
          _onCorrectWord(matchResult);
        } else {
          _onIncorrectWord(matchResult);
        }
      },
    );
  }

  /// Handles correct word recognition
  void _onCorrectWord(WordMatchResult matchResult) {
    // Play success sound
    AudioUtils.playSuccessSound();

    // Update revealed words
    final updatedRevealed = List<String>.from(state.revealedWords);
    updatedRevealed[state.currentWordIndex] = matchResult.expectedWord;

    final nextIndex = state.currentWordIndex + 1;
    final isCompleted = nextIndex >= state.session!.words.length;

    state = state.copyWith(
      revealedWords: updatedRevealed,
      currentWordIndex: isCompleted ? state.currentWordIndex : nextIndex,
      isCompleted: isCompleted,
    );

    if (isCompleted) {
      _onSessionCompleted();
    }
  }

  /// Handles incorrect word recognition
  void _onIncorrectWord(WordMatchResult matchResult) {
    // Play error sound
    AudioUtils.playErrorSound();

    // The user needs to try again - no state change needed
    // The UI will show visual feedback for the incorrect attempt
  }

  /// Handles session completion
  void _onSessionCompleted() {
    _stopListening();

    // Update session with completion time
    if (state.session != null) {
      final completedSession = state.session!.copyWith(
        isCompleted: true,
        completedAt: DateTime.now(),
      );
      state = state.copyWith(session: completedSession);
    }
  }

  /// Handles speech recognition errors
  void _onSpeechError(dynamic error) {
    // Only show critical errors to the user
    // Timeout and no match errors are normal and shouldn't be displayed
    if (_isCriticalSpeechError(error)) {
      state = state.copyWith(
        hasError: true,
        errorMessage: 'Speech recognition error: $error',
        isListening: false,
      );
    } else {
      // For non-critical errors, just stop listening without showing error
      // and automatically restart listening after a short delay
      state = state.copyWith(isListening: false);
      _autoRestartListening();
    }
  }

  /// Automatically restarts listening after non-critical errors
  void _autoRestartListening() {
    if (state.isCompleted || state.session == null) return;

    // Wait a short moment before restarting
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!state.isCompleted && state.session != null && !state.isListening) {
        _startListening();
      }
    });
  }

  /// Determines if a speech recognition error is critical and should be shown to user
  bool _isCriticalSpeechError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return !errorString.contains('timeout') &&
        !errorString.contains('no_match') &&
        !errorString.contains('no match') &&
        !errorString.contains('busy') &&
        !errorString.contains('audio');
  }

  /// Stops listening for speech input
  Future<void> _stopListening() async {
    try {
      await _speechDatasource.stopListening();
      await _speechSubscription?.cancel();
      await _partialSpeechSubscription?.cancel();
      _speechSubscription = null;
      _partialSpeechSubscription = null;

      state = state.copyWith(
        isListening: false,
        currentTranscription: '', // Clear transcription when stopping
      );
    } catch (e) {
      // Ignore stop listening errors
    }
  }

  /// Restarts listening (useful when user wants to try again)
  Future<void> restartListening() async {
    if (state.isCompleted) return;

    await _stopListening();
    await _startListening();
  }

  /// Resets the session
  Future<void> resetSession() async {
    await _stopListening();

    if (state.session != null) {
      state = state.copyWith(
        revealedWords: List.filled(state.session!.words.length, ''),
        currentWordIndex: 0,
        isCompleted: false,
        hasError: false,
        errorMessage: null,
        lastSpokenWord: null,
        lastMatchResult: null,
      );

      await _startListening();
    }
  }

  /// Clears error messages
  void clearError() {
    state = state.copyWith(hasError: false, errorMessage: null);
  }

  @override
  void dispose() {
    _stopListening();
    _speechSubscription?.cancel();
    _partialSpeechSubscription?.cancel();
    _speechDatasource.dispose();
    super.dispose();
  }
}

// Provider for the controller
final memorizationControllerProvider =
    StateNotifierProvider<MemorizationController, MemorizationState>((ref) {
      return MemorizationController(
        startSession: getIt<StartSession>(),
        processSpeech: getIt<ProcessSpeech>(),
        validateWord: getIt<ValidateWord>(),
        speechDatasource: getIt<SpeechRecognitionDatasource>(),
      );
    });
