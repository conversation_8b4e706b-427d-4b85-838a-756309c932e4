import 'package:flutter/material.dart';

class RealTimeTranscriptionDisplay extends StatefulWidget {
  final String transcriptionText;
  final bool isListening;
  final bool isVisible;

  const RealTimeTranscriptionDisplay({
    super.key,
    required this.transcriptionText,
    required this.isListening,
    this.isVisible = true,
  });

  @override
  State<RealTimeTranscriptionDisplay> createState() =>
      _RealTimeTranscriptionDisplayState();
}

class _RealTimeTranscriptionDisplayState
    extends State<RealTimeTranscriptionDisplay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    if (widget.isListening) {
      _animationController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(RealTimeTranscriptionDisplay oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isListening != oldWidget.isListening) {
      if (widget.isListening) {
        _animationController.repeat(reverse: true);
      } else {
        _animationController.stop();
        _animationController.reset();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      height: widget.isListening || widget.transcriptionText.isNotEmpty
          ? null
          : 0,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: _getBackgroundColor(context),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: _getBorderColor(context), width: 2),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with listening indicator
            _buildHeader(context),

            if (widget.transcriptionText.isNotEmpty || widget.isListening) ...[
              const SizedBox(height: 12),
              _buildTranscriptionText(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        // Listening indicator
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: widget.isListening ? _pulseAnimation.value : 1.0,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: widget.isListening ? Colors.red : Colors.grey,
                  shape: BoxShape.circle,
                ),
              ),
            );
          },
        ),

        const SizedBox(width: 8),

        // Status text
        Text(
          widget.isListening ? 'جاري الاستماع...' : 'في انتظار الصوت',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: widget.isListening ? Colors.red : Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),

        const Spacer(),

        // Microphone icon
        Icon(
          widget.isListening ? Icons.mic : Icons.mic_off,
          size: 16,
          color: widget.isListening ? Colors.red : Colors.grey,
        ),
      ],
    );
  }

  Widget _buildTranscriptionText(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 200),
      child: Container(
        key: ValueKey(widget.transcriptionText),
        width: double.infinity,
        constraints: const BoxConstraints(minHeight: 40),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300, width: 1),
        ),
        child: widget.transcriptionText.isNotEmpty
            ? Text(
                widget.transcriptionText,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).textTheme.bodyLarge?.color,
                  height: 1.4,
                ),
                textAlign: TextAlign.right,
                textDirection: TextDirection.rtl,
              )
            : Text(
                'تحدث الآن...',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[500],
                  fontStyle: FontStyle.italic,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
              ),
      ),
    );
  }

  Color _getBackgroundColor(BuildContext context) {
    if (widget.isListening) {
      return Colors.red.shade50;
    }
    return Theme.of(context).cardColor;
  }

  Color _getBorderColor(BuildContext context) {
    if (widget.isListening) {
      return Colors.red.shade200;
    }
    return Colors.grey.shade300;
  }
}
