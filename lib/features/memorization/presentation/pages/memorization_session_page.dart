import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auto_route/auto_route.dart';
import '../../../../app/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/widgets/loading_indicator.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../text_management/domain/entities/saved_text.dart';
import '../controllers/memorization_controller.dart';
import '../widgets/word_placeholder_widget.dart';
import '../widgets/session_controls.dart';
import '../widgets/completion_dialog.dart';
import '../widgets/real_time_transcription_display.dart';

class MemorizationSessionPageWidget extends ConsumerStatefulWidget {
  final SavedText savedText;

  const MemorizationSessionPageWidget({super.key, required this.savedText});

  @override
  ConsumerState<MemorizationSessionPageWidget> createState() =>
      _MemorizationSessionPageWidgetState();
}

class _MemorizationSessionPageWidgetState
    extends ConsumerState<MemorizationSessionPageWidget> {
  @override
  void initState() {
    super.initState();
    // Start the memorization session when the page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref
          .read(memorizationControllerProvider.notifier)
          .startMemorizationSession(widget.savedText);
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(memorizationControllerProvider);
    final controller = ref.read(memorizationControllerProvider.notifier);

    // Listen for session completion
    ref.listen<MemorizationState>(memorizationControllerProvider, (
      previous,
      next,
    ) {
      if (next.isCompleted && previous?.isCompleted != true) {
        _showCompletionDialog(context);
      }

      // Show error messages
      if (next.hasError && next.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.errorMessage!),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Dismiss',
              onPressed: () => controller.clearError(),
            ),
          ),
        );
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.savedText.title),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.router.maybePop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: state.isInitializing
                ? null
                : () => controller.resetSession(),
          ),
        ],
      ),
      body: _buildBody(state, controller),
    );
  }

  Widget _buildBody(
    MemorizationState state,
    MemorizationController controller,
  ) {
    if (state.isInitializing) {
      return const LoadingIndicator(message: 'Initializing session...');
    }

    if (state.session == null) {
      return _buildErrorState(controller);
    }

    return Column(
      children: [
        // Progress indicator
        _buildProgressIndicator(state),

        // Instructions
        _buildInstructions(state),

        // Real-time transcription display
        RealTimeTranscriptionDisplay(
          transcriptionText: state.currentTranscription,
          isListening: state.isListening,
          isVisible: state.isListening || state.currentTranscription.isNotEmpty,
        ),

        // Word placeholders
        Expanded(child: _buildWordGrid(state)),

        // Session controls
        SessionControls(
          isListening: state.isListening,
          isCompleted: state.isCompleted,
          onStartListening: () => controller.restartListening(),
          onStopListening: () => controller.restartListening(),
          onReset: () => controller.resetSession(),
        ),
      ],
    );
  }

  Widget _buildProgressIndicator(MemorizationState state) {
    final progress = state.currentWordIndex / state.session!.words.length;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${state.currentWordIndex} / ${state.session!.words.length} words',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions(MemorizationState state) {
    String instruction;
    Color instructionColor;

    if (state.isCompleted) {
      instruction = AppConstants.congratulationsMessage;
      instructionColor = Colors.green;
    } else if (state.isListening) {
      instruction = AppConstants.listeningMessage;
      instructionColor = Colors.blue;
    } else {
      instruction = AppConstants.sessionStartMessage;
      instructionColor = Colors.grey[600]!;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Text(
        instruction,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: instructionColor,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildWordGrid(MemorizationState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Wrap(
        spacing: 12,
        runSpacing: 12,
        children: List.generate(
          state.session!.words.length,
          (index) => WordPlaceholderWidget(
            word: state.session!.words[index],
            revealedWord: state.revealedWords[index],
            isCurrentWord: index == state.currentWordIndex,
            isRevealed: state.revealedWords[index].isNotEmpty,
            showError:
                state.lastMatchResult?.isMatch == false &&
                index == state.currentWordIndex,
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(MemorizationController controller) {
    final state = ref.watch(memorizationControllerProvider);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.mic_off, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'فشل في بدء جلسة الحفظ',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            if (state.errorMessage != null)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Text(
                  state.errorMessage!,
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.red.shade700),
                  textAlign: TextAlign.center,
                ),
              ),
            const SizedBox(height: 24),
            _buildTroubleshootingTips(),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    text: 'العودة',
                    onPressed: () => context.router.maybePop(),
                    isOutlined: true,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomButton(
                    text: 'إعادة المحاولة',
                    onPressed: () =>
                        controller.startMemorizationSession(widget.savedText),
                    icon: Icons.refresh,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTroubleshootingTips() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue.shade700, size: 20),
              const SizedBox(width: 8),
              Text(
                'نصائح لحل المشكلة:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildTip('تأكد من أن الجهاز يدعم التعرف على الصوت'),
          _buildTip('امنح التطبيق صلاحية الوصول للميكروفون'),
          _buildTip('تأكد من أن الميكروفون يعمل بشكل صحيح'),
          _buildTip('جرب إعادة تشغيل التطبيق'),
        ],
      ),
    );
  }

  Widget _buildTip(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ', style: TextStyle(color: Colors.blue.shade700)),
          Expanded(
            child: Text(
              text,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.blue.shade700),
            ),
          ),
        ],
      ),
    );
  }

  void _showCompletionDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => CompletionDialog(
        onRestart: () {
          Navigator.of(context).pop();
          ref.read(memorizationControllerProvider.notifier).resetSession();
        },
        onExit: () {
          Navigator.of(context).pop();
          context.router.maybePop();
        },
      ),
    );
  }
}
