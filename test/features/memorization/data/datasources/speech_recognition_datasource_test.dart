import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:permission_handler/permission_handler.dart';

import 'package:hafiz_app/features/memorization/data/datasources/speech_recognition_datasource.dart';
import 'package:hafiz_app/core/errors/exceptions.dart';

// Generate mocks
@GenerateMocks([SpeechToText])
import 'speech_recognition_datasource_test.mocks.dart';

void main() {
  group('SpeechRecognitionDatasourceImpl', () {
    late SpeechRecognitionDatasourceImpl datasource;
    late MockSpeechToText mockSpeechToText;

    setUp(() {
      mockSpeechToText = MockSpeechToText();
      datasource = SpeechRecognitionDatasourceImpl();
      // We would need to inject the mock, but for now this tests the error handling logic
    });

    group('_isCriticalError', () {
      test('should return false for non-critical errors', () {
        expect(datasource._isCriticalError('error_speech_timeout'), false);
        expect(datasource._isCriticalError('error_no_match'), false);
        expect(datasource._isCriticalError('error_busy'), false);
        expect(datasource._isCriticalError('error_audio'), false);
      });

      test('should return true for critical errors', () {
        expect(datasource._isCriticalError('error_network'), true);
        expect(datasource._isCriticalError('error_server'), true);
        expect(datasource._isCriticalError('error_client'), true);
        expect(datasource._isCriticalError('unknown_error'), true);
      });
    });

    group('initialize', () {
      test('should not throw exception for timeout errors', () async {
        // This test would require proper mocking setup
        // For now, it serves as documentation of expected behavior
        expect(() => datasource._isCriticalError('error_speech_timeout'), 
               returnsNormally);
      });
    });
  });
}
